package TcpPoll

import (
	"errors"
	"sync"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/comm"

	"github.com/astaxie/beego"
	log "github.com/sirupsen/logrus"
)

type TcpManager struct {
	running        bool      // 控制是否消息loop
	connections    *sync.Map // 以关键字为key的连接池, 用于发送 string -> *TcpClient
	fdConnections  *sync.Map // 以fd为key的连接池, 用于接收 int -> *TcpClient
	poll           *epoll
	stopChan       chan struct{} // 停止通道
	workerStopChan chan struct{} // 工作线程停止通道
}

// TcpManager单例, 使用sync.Once.Do解决并发时多次创建
var once sync.Once
var instance *TcpManager

// 获取单例Tcp
func GetTcpManager() (*TcpManager, error) {
	var err error
	longLinkEnabled, _ := beego.AppConfig.Bool("longlinkenabled")
	if !longLinkEnabled {
		return nil, errors.New("不支持长连接请求")
	}
	once.Do(func() {
		var epollInstance *epoll
		epollInstance, err = MkEpoll()
		if err != nil {
			return
		}
		instance = &TcpManager{
			running:        true,
			connections:    &sync.Map{},
			fdConnections:  &sync.Map{},
			poll:           epollInstance,
			stopChan:       make(chan struct{}),
			workerStopChan: make(chan struct{}),
		}
	})
	return instance, err
}

// 队列增加长连接. key: 关键字, conn: 长连接
func (manager *TcpManager) Add(key string, client *TcpClient) error {
	fd, err := manager.poll.Add(client.conn) // 将长连接增加到epoll
	if err != nil {
		return err
	}
	// 增加对照表
	manager.connections.Store(key, client)
	manager.fdConnections.Store(fd, client)
	return nil
}

// 队列移除长连接. client: TcpClient
func (manager *TcpManager) Remove(client *TcpClient) {
	fd := socketFD(client.conn)
	client.Terminate()
	manager.poll.Remove(client.conn)
	manager.connections.Delete(client.model.Wxid)
	manager.fdConnections.Delete(fd)
	client = nil
}

// 创建长连接并添加到epoll.
func (manager *TcpManager) GetClient(loginData *comm.LoginData) (*TcpClient, error) {
	// 根据key查找是否存在已有连接, 如果已存在, 则返回
	if clientInterface, ok := manager.connections.Load(loginData.Wxid); ok {
		if client, ok := clientInterface.(*TcpClient); ok && client != nil {
			client.model = loginData
			return client, nil
		}
	}
	// 检查MarsHost
	if loginData.MarsHost == "" {
		loginData.MarsHost = Algorithm.MmtlsLongHost
	}
	// 创建新的连接
	client := NewTcpClient(loginData)
	if err := client.Connect(); err != nil {
		return nil, err
	}
	// 将完成连接的client添加到epoll
	if err := manager.Add(loginData.Wxid, client); err != nil {
		return nil, err
	}
	timeoutSpan, _ := time.ParseDuration(beego.AppConfig.String("longlinkconnecttimeout"))
	timeoutTime := time.Now().Add(timeoutSpan)
	// 进入循环等待, 完成握手或者超时都将退出循环
	for time.Now().Before(timeoutTime) {
		time.Sleep(100 * time.Millisecond)
		// 通过client.handshaking判断是否已经完成握手
		if !client.handshaking {
			break
		}
	}
	if client.handshaking {
		// 超时没有完成握手, 报错
		manager.Remove(client)
		return nil, errors.New("mmtls握手超时")
	}

	return client, nil
}

// 循环接收消息
func (manager *TcpManager) RunEventLoop() {
	// 创建一个有界工作池，控制并发数量（支持配置化）
	workerCount := beego.AppConfig.DefaultInt("tcp.worker.count", 10000) // 并发工作线程数量
	queueSize := beego.AppConfig.DefaultInt("tcp.queue.size", 20000)     // 任务队列容量
	taskQueue := make(chan *TcpClient, queueSize)

	// 启动工作线程池
	for i := 0; i < workerCount; i++ {
		go func() {
			for {
				select {
				case <-manager.workerStopChan:
					log.Printf("TcpManager: 工作线程退出")
					return
				case client := <-taskQueue:
					if client == nil {
						continue
					}
					// 使用匿名函数包装执行过程，方便处理panic
					func(c *TcpClient) {
						defer func() {
							if err := recover(); err != nil {
								log.Printf("client.Once panic: %v", err)
							}
						}()
						c.Once() // Once方法内部会处理并发控制
					}(client)
				}
			}
		}()
	}

	// 主循环，监听停止信号和epoll事件
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	for manager.running {
		select {
		case <-manager.stopChan:
			log.Printf("TcpManager: 收到停止信号，开始优雅关闭")
			manager.running = false
			goto cleanup
		case <-ticker.C:
			fds, waitErr := manager.poll.Wait()
			if waitErr != nil {
				log.Printf("failed to epoll wait %v", waitErr)
				continue
			}
			if len(fds) == 0 {
				continue
			}
			// fds为有收到消息的连接文件描述
			for _, fd := range fds {
				if clientInterface, ok := manager.fdConnections.Load(fd); ok {
					if client, ok := clientInterface.(*TcpClient); ok && client != nil {
						// 检查连接是否有错误，如果有则清理
						if client.hasError {
							log.Infof("TcpManager: 清理错误连接[%s]", client.model.Wxid)
							manager.Remove(client)
							continue
						}

						// 将客户端任务提交到工作池
						select {
						case taskQueue <- client:
							// 任务成功提交到队列
						default:
							// 队列已满，直接在主线程中处理，避免丢失消息
							client.Once()
							// 处理后再次检查是否有错误
							if client.hasError {
								log.Infof("TcpManager: 处理后发现连接错误，清理连接[%s]", client.model.Wxid)
								manager.Remove(client)
							}
						}
					}
				}
			}
		}
	}

cleanup:
	log.Printf("TcpManager: 开始清理资源")

	// 通知所有工作线程退出
	close(manager.workerStopChan)

	// 关闭任务队列
	close(taskQueue)

	// 关闭所有连接
	manager.connections.Range(func(key, value interface{}) bool {
		if client, ok := value.(*TcpClient); ok && client != nil {
			client.Terminate()
		}
		return true
	})

	log.Printf("TcpManager: 资源清理完成")
}

// 停止TcpManager
func (manager *TcpManager) Stop() {
	if manager.stopChan != nil {
		select {
		case <-manager.stopChan:
			// 已经关闭
		default:
			close(manager.stopChan)
		}
	}
}
