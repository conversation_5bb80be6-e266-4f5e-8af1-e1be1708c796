module wechatdll

go 1.23

toolchain go1.23.0

require (
	github.com/PuerkitoBio/goquery v1.10.1
	github.com/astaxie/beego v1.12.3
	github.com/bitly/go-simplejson v0.5.1
	github.com/boombuler/barcode v1.0.2
	github.com/chromedp/chromedp v0.12.1
	github.com/go-redis/redis v6.15.9+incompatible
	github.com/gogf/guuid v1.1.0
	github.com/gogo/protobuf v1.3.2
	github.com/golang/protobuf v1.5.4
	github.com/klauspost/compress v1.17.11
	github.com/lunny/log v0.0.0-20160921050905-7887c61bf0de
	github.com/sirupsen/logrus v1.9.3
	github.com/wsddn/go-ecdh v0.0.0-20161211032359-48726bab9208
	golang.org/x/crypto v0.32.0
	golang.org/x/net v0.34.0
	golang.org/x/sys v0.29.0
	google.golang.org/protobuf v1.36.4
)

require (
	github.com/andybalholm/cascadia v1.3.3 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/chromedp/cdproto v0.0.0-20250120090109-d38428e4d9c8 // indirect
	github.com/chromedp/sysutil v1.1.0 // indirect
	github.com/gobwas/httphead v0.1.0 // indirect
	github.com/gobwas/pool v0.2.1 // indirect
	github.com/gobwas/ws v1.4.0 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/hashicorp/golang-lru v1.0.2 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/kr/text v0.2.0 // indirect
	github.com/mailru/easyjson v0.9.0 // indirect
	github.com/munnerz/goautoneg v0.0.0-20191010083416-a7dc8b61c822 // indirect
	github.com/prometheus/client_golang v1.20.5 // indirect
	github.com/prometheus/client_model v0.6.1 // indirect
	github.com/prometheus/common v0.62.0 // indirect
	github.com/prometheus/procfs v0.15.1 // indirect
	github.com/shiena/ansicolor v0.0.0-20230509054315-a9deabde6e02 // indirect
	golang.org/x/text v0.21.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
)
