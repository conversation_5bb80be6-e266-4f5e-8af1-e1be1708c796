package controllers

import (
	"encoding/json"
	"net/http"

	"github.com/astaxie/beego"
)

type BaseController struct {
	beego.Controller
}

func (c *BaseController) ServeJSON() {
	c.Ctx.Output.Header("Content-Type", "application/json; charset=utf-8")

	var err error
	data := c.Data["json"]

	// 先序列化到字节数组，避免直接写入ResponseWriter导致提前调用WriteHeader
	jsonBytes, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		c.Ctx.Output.SetStatus(http.StatusInternalServerError)
		c.Ctx.Output.Body([]byte(err.Error()))
		return
	}

	// 统一写入响应
	c.Ctx.Output.Body(jsonBytes)
}
